const {constant} = require("../constant")
const errorHandler = (err, req, res, next) => {
    const statusCode = res.statusCode ? res.statusCode : 500;

    switch (statusCode) {
         case constant.not_found:
            res.json({ title: "Not_Found", message: err.message, stackTrace: err.stack });
            break;

         case constant.validation_error:
            res.json({ title: "Validation Failed", message: err.message, stackTrace: err.stack });
            break;

         case constant.unautherized:
            res.json({ title: "Unauthorized", message: err.message, stackTrace: err.stack });
            break;

         case constant.forbidden:
            res.json({ title: "Forbidden", message: err.message, stackTrace: err.stack });
            break;

         case constant.server_error:
            res.json({ title: "Server Error", message: err.message, stackTrace: err.stack });
            break;
        default:
            console.log("no error , All good !");
            break;

    }
};
module.exports = errorHandler;