const {constants} = require("../constant")
const errorHandler = (err, req, res, next) => {
    const statusCode = res.statusCode ? res.statusCode : 500;

    switch (statusCode) {
         case constants.not_found:
            res.json({ title: "Not_Found", message: err.message, stackTrace: err.stack });

            break;
    
         case constants.validation_error:
            res.json({ title: "Validation Faild", message: err.message, stackTrace: err.stack });

            break;
     
         case constants.unautherized:
            res.json({ title: "unautherized", message: err.message, stackTrace: err.stack });

            break;

         case constants.forbidden:
            res.json({ title: "forbidden", message: err.message, stackTrace: err.stack });

            break;

         case constants.server_error:
            res.json({ title: "server_error", message: err.message, stackTrace: err.stack });

            break;
        default:
            console.log("no error , All good !");
            break;

    }
};
module.exports = errorHandler;