const { MongoClient } = require('mongodb');

let db;

const connectDb = async () => {
    try {
        const client = new MongoClient(process.env.CONNECTION_STRING);
        await client.connect();
        console.log("Database connected successfully");
        db = client.db(); // This will use the database specified in the connection string
        return db;
    } catch (err) {
        console.error("Database connection failed:", err);
        process.exit(1);
    }
};

const getDb = () => {
    if (!db) {
        throw new Error('Database not connected. Call connectDb first.');
    }
    return db;
};

module.exports = { connectDb, getDb };
