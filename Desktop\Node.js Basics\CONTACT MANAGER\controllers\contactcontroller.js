const asyncHandler = require("express-async-handler");
const { getDb } = require("../config/dbConnection");
const Contact = require("../models/contactModel");
const { ObjectId } = require('mongodb');

//@desc create new contact
//@route POST /api/contact
//@access public

const CreateNewContact = asyncHandler(async(req, res) => {
    console.log("Request body:", req.body);
    const { name, email, phone } = req.body;

    // Validate input data
    const validation = Contact.validate({ name, email, phone });
    if (!validation.isValid) {
        res.status(400);
        throw new Error(validation.errors.join(', '));
    }

    try {
        const db = getDb();
        const contactsCollection = db.collection('contacts');

        // Check if contact with same email already exists
        const existingContact = await contactsCollection.findOne({ email: email });
        if (existingContact) {
            res.status(400);
            throw new Error("Contact with this email already exists");
        }

        // Create new contact
        const newContact = new Contact(name, email, phone);
        const result = await contactsCollection.insertOne(newContact.toDocument());

        if (result.insertedId) {
            const createdContact = await contactsCollection.findOne({ _id: result.insertedId });
            res.status(201).json({
                message: "Contact created successfully",
                contact: createdContact
            });
        } else {
            res.status(500);
            throw new Error("Failed to create contact");
        }
    } catch (error) {
        res.status(500);
        throw new Error(`Error creating contact: ${error.message}`);
    }
});


//@desc get all contacts
//@route GET /api/contact
//@access public

const getContact = asyncHandler(async(req, res) => {
    try {
        const db = getDb();
        const contactsCollection = db.collection('contacts');

        // Get all contacts
        const contacts = await contactsCollection.find({}).toArray();

        res.status(200).json({
            message: "Contacts retrieved successfully",
            count: contacts.length,
            contacts: contacts
        });
    } catch (error) {
        res.status(500);
        throw new Error(`Error retrieving contacts: ${error.message}`);
    }
});




//@desc get particular contact by ID
//@route GET /api/contact/:id
//@access public

const getContactbyid = asyncHandler(async(req, res) => {
    const { id } = req.params;

    // Validate ObjectId
    if (!Contact.isValidObjectId(id)) {
        res.status(400);
        throw new Error("Invalid contact ID format");
    }

    try {
        const db = getDb();
        const contactsCollection = db.collection('contacts');

        // Find contact by ID
        const contact = await contactsCollection.findOne({ _id: new ObjectId(id) });

        if (!contact) {
            res.status(404);
            throw new Error("Contact not found");
        }

        res.status(200).json({
            message: "Contact retrieved successfully",
            contact: contact
        });
    } catch (error) {
        if (error.message === "Contact not found") {
            res.status(404);
        } else {
            res.status(500);
        }
        throw new Error(`Error retrieving contact: ${error.message}`);
    }
});


//@desc update contact by ID
//@route PUT /api/contact/:id
//@access public

const updateContact = asyncHandler(async(req, res) => {
    const { id } = req.params;
    const { name, email, phone } = req.body;

    // Validate ObjectId
    if (!Contact.isValidObjectId(id)) {
        res.status(400);
        throw new Error("Invalid contact ID format");
    }

    // Validate input data
    const validation = Contact.validate({ name, email, phone });
    if (!validation.isValid) {
        res.status(400);
        throw new Error(validation.errors.join(', '));
    }

    try {
        const db = getDb();
        const contactsCollection = db.collection('contacts');

        // Check if contact exists
        const existingContact = await contactsCollection.findOne({ _id: new ObjectId(id) });
        if (!existingContact) {
            res.status(404);
            throw new Error("Contact not found");
        }

        // Check if email is being changed and if new email already exists
        if (email !== existingContact.email) {
            const emailExists = await contactsCollection.findOne({
                email: email,
                _id: { $ne: new ObjectId(id) }
            });
            if (emailExists) {
                res.status(400);
                throw new Error("Contact with this email already exists");
            }
        }

        // Update contact
        const updateData = {
            name: name,
            email: email,
            phone: phone,
            updatedAt: new Date()
        };

        const result = await contactsCollection.updateOne(
            { _id: new ObjectId(id) },
            { $set: updateData }
        );

        if (result.modifiedCount === 1) {
            const updatedContact = await contactsCollection.findOne({ _id: new ObjectId(id) });
            res.status(200).json({
                message: "Contact updated successfully",
                contact: updatedContact
            });
        } else {
            res.status(500);
            throw new Error("Failed to update contact");
        }
    } catch (error) {
        if (error.message === "Contact not found") {
            res.status(404);
        } else if (error.message.includes("already exists")) {
            res.status(400);
        } else {
            res.status(500);
        }
        throw new Error(`Error updating contact: ${error.message}`);
    }
});




//@desc delete contact by ID
//@route DELETE /api/contact/:id
//@access public

const deleteContact = asyncHandler(async(req, res) => {
    const { id } = req.params;

    // Validate ObjectId
    if (!Contact.isValidObjectId(id)) {
        res.status(400);
        throw new Error("Invalid contact ID format");
    }

    try {
        const db = getDb();
        const contactsCollection = db.collection('contacts');

        // Check if contact exists
        const existingContact = await contactsCollection.findOne({ _id: new ObjectId(id) });
        if (!existingContact) {
            res.status(404);
            throw new Error("Contact not found");
        }

        // Delete contact
        const result = await contactsCollection.deleteOne({ _id: new ObjectId(id) });

        if (result.deletedCount === 1) {
            res.status(200).json({
                message: "Contact deleted successfully",
                deletedContact: existingContact
            });
        } else {
            res.status(500);
            throw new Error("Failed to delete contact");
        }
    } catch (error) {
        if (error.message === "Contact not found") {
            res.status(404);
        } else {
            res.status(500);
        }
        throw new Error(`Error deleting contact: ${error.message}`);
    }
});

module.exports={getContact,CreateNewContact,getContactbyid,updateContact,deleteContact};
