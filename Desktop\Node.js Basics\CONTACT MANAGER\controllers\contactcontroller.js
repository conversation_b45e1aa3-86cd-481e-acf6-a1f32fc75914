const asyncHandler = require("express-async-handler")

//@desc create all contact 
//@route put/api/contact
//@access public

const CreateNewContact =asyncHandler(async(req,res)=>{
    console.log("the req body is :",req.body)
    const{Name,Email,Phone}=req.body;   
    if(!Name ||!Email||!Phone){
        res.status(400);
        throw new Error("All fields are mandotory")
    }
    res.status(200).json({message:"Create newcontact "})
});


//@desc get all contact 
//@route get/api/contact
//@access public

const getContact =async(req,res)=>{
    res.status(200).json({message:`get  all contact  `})
}




//@desc get perticular contact 
//@route get/api/contact
//@access public

const getContactbyid =asyncHandler(async(req,res)=>{
    res.status(200).json({message:`get  contact for ${req.params.id} `})
});


//@desc update  contact  by id 
//@route put/api/contact
//@access public

const updateContact =asyncHandler(async(req,res)=>{
    res.status(200).json({message:`Update  contact for  ${req.params.id} `})
});




//@desc delete contact 
//@route delete/api/contact
//@access public

const deleteContact =asyncHandler(async(req,res)=>{
    res.status(200).json({message:`delete  contact for ${req.params.id} `})
});

module.exports={getContact,CreateNewContact,getContactbyid,updateContact,deleteContact};
