# Manual Contact Addition Guide

Since your server is running on port 5001, you can add contacts manually using these methods:

## Method 1: Using PowerShell (Recommended)

Copy and paste these commands one by one in PowerShell:

```powershell
# Contact 1: <PERSON>
Invoke-RestMethod -Uri "http://localhost:5001/api/contact" -Method Post -Body '{"name":"<PERSON>","email":"<EMAIL>","phone":"******-0101"}' -ContentType "application/json"

# Contact 2: <PERSON>
Invoke-RestMethod -Uri "http://localhost:5001/api/contact" -Method Post -Body '{"name":"<PERSON>","email":"<EMAIL>","phone":"******-0102"}' -ContentType "application/json"

# Contact 3: <PERSON>
Invoke-RestMethod -Uri "http://localhost:5001/api/contact" -Method Post -Body '{"name":"<PERSON>","email":"<EMAIL>","phone":"******-0103"}' -ContentType "application/json"

# Contact 4: <PERSON>
Invoke-RestMethod -Uri "http://localhost:5001/api/contact" -Method Post -Body '{"name":"<PERSON>","email":"<EMAIL>","phone":"******-0104"}' -ContentType "application/json"

# Contact 5: David Brown
Invoke-RestMethod -Uri "http://localhost:5001/api/contact" -Method Post -Body '{"name":"David Brown","email":"<EMAIL>","phone":"******-0105"}' -ContentType "application/json"

# Contact 6: Emily Davis
Invoke-RestMethod -Uri "http://localhost:5001/api/contact" -Method Post -Body '{"name":"Emily Davis","email":"<EMAIL>","phone":"******-0106"}' -ContentType "application/json"

# Contact 7: Robert Miller
Invoke-RestMethod -Uri "http://localhost:5001/api/contact" -Method Post -Body '{"name":"Robert Miller","email":"<EMAIL>","phone":"******-0107"}' -ContentType "application/json"

# Contact 8: Lisa Anderson
Invoke-RestMethod -Uri "http://localhost:5001/api/contact" -Method Post -Body '{"name":"Lisa Anderson","email":"<EMAIL>","phone":"******-0108"}' -ContentType "application/json"
```

## Method 2: Using curl (if available)

```bash
# Contact 1
curl -X POST http://localhost:5001/api/contact -H "Content-Type: application/json" -d "{\"name\":\"John Doe\",\"email\":\"<EMAIL>\",\"phone\":\"******-0101\"}"

# Contact 2
curl -X POST http://localhost:5001/api/contact -H "Content-Type: application/json" -d "{\"name\":\"Jane Smith\",\"email\":\"<EMAIL>\",\"phone\":\"******-0102\"}"

# Contact 3
curl -X POST http://localhost:5001/api/contact -H "Content-Type: application/json" -d "{\"name\":\"Mike Johnson\",\"email\":\"<EMAIL>\",\"phone\":\"******-0103\"}"

# Contact 4
curl -X POST http://localhost:5001/api/contact -H "Content-Type: application/json" -d "{\"name\":\"Sarah Wilson\",\"email\":\"<EMAIL>\",\"phone\":\"******-0104\"}"

# Contact 5
curl -X POST http://localhost:5001/api/contact -H "Content-Type: application/json" -d "{\"name\":\"David Brown\",\"email\":\"<EMAIL>\",\"phone\":\"******-0105\"}"
```

## Method 3: Using Postman or Thunder Client

1. Open Postman or Thunder Client
2. Create a new POST request
3. URL: `http://localhost:5001/api/contact`
4. Headers: `Content-Type: application/json`
5. Body (raw JSON):

```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "******-0101"
}
```

Repeat for each contact with different data.

## Method 4: Using Browser Console

Open your browser, go to any page, open Developer Tools (F12), go to Console tab, and run:

```javascript
fetch('http://localhost:5001/api/contact', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-0101'
    })
})
.then(response => response.json())
.then(data => console.log(data));
```

## Verify Contacts Added

After adding contacts, verify by getting all contacts:

**PowerShell:**
```powershell
Invoke-RestMethod -Uri "http://localhost:5001/api/contact" -Method Get
```

**curl:**
```bash
curl http://localhost:5001/api/contact
```

**Browser:**
Navigate to: `http://localhost:5001/api/contact`

## Sample Contacts Data

Here are 8 sample contacts you can add:

1. **John Doe** - <EMAIL> - ******-0101
2. **Jane Smith** - <EMAIL> - ******-0102
3. **Mike Johnson** - <EMAIL> - ******-0103
4. **Sarah Wilson** - <EMAIL> - ******-0104
5. **David Brown** - <EMAIL> - ******-0105
6. **Emily Davis** - <EMAIL> - ******-0106
7. **Robert Miller** - <EMAIL> - ******-0107
8. **Lisa Anderson** - <EMAIL> - ******-0108

Choose the method that works best for you!
