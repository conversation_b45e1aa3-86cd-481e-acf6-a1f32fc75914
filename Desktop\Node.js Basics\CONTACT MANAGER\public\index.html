<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Manager</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-address-book"></i> Contact Manager</h1>
                <p>Manage your contacts efficiently</p>
            </div>
        </header>

        <!-- Add Contact Form -->
        <section class="add-contact-section">
            <div class="form-container">
                <h2><i class="fas fa-user-plus"></i> Add New Contact</h2>
                <form id="contactForm" class="contact-form">
                    <div class="form-group">
                        <label for="name">
                            <i class="fas fa-user"></i> Name
                        </label>
                        <input type="text" id="name" name="name" required placeholder="Enter full name">
                    </div>
                    
                    <div class="form-group">
                        <label for="email">
                            <i class="fas fa-envelope"></i> Email
                        </label>
                        <input type="email" id="email" name="email" required placeholder="Enter email address">
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">
                            <i class="fas fa-phone"></i> Phone
                        </label>
                        <input type="tel" id="phone" name="phone" required placeholder="Enter phone number">
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Contact
                        </button>
                        <button type="button" id="cancelEdit" class="btn btn-secondary" style="display: none;">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </form>
            </div>
        </section>

        <!-- Search and Filter -->
        <section class="search-section">
            <div class="search-container">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="Search contacts by name, email, or phone...">
                </div>
                <button id="refreshBtn" class="btn btn-outline">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </section>

        <!-- Contacts List -->
        <section class="contacts-section">
            <div class="contacts-header">
                <h2><i class="fas fa-list"></i> Your Contacts</h2>
                <span id="contactCount" class="contact-count">0 contacts</span>
            </div>
            
            <div id="loadingSpinner" class="loading">
                <i class="fas fa-spinner fa-spin"></i> Loading contacts...
            </div>
            
            <div id="contactsList" class="contacts-list">
                <!-- Contacts will be dynamically loaded here -->
            </div>
            
            <div id="noContacts" class="no-contacts" style="display: none;">
                <i class="fas fa-address-book"></i>
                <h3>No contacts found</h3>
                <p>Start by adding your first contact above!</p>
            </div>
        </section>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> Edit Contact</h3>
                <span class="close">&times;</span>
            </div>
            <form id="editForm" class="contact-form">
                <input type="hidden" id="editId">
                
                <div class="form-group">
                    <label for="editName">
                        <i class="fas fa-user"></i> Name
                    </label>
                    <input type="text" id="editName" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="editEmail">
                        <i class="fas fa-envelope"></i> Email
                    </label>
                    <input type="email" id="editEmail" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="editPhone">
                        <i class="fas fa-phone"></i> Phone
                    </label>
                    <input type="tel" id="editPhone" name="phone" required>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Contact
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeEditModal()">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-trash"></i> Delete Contact</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this contact?</p>
                <div id="deleteContactInfo" class="contact-info"></div>
            </div>
            <div class="form-actions">
                <button id="confirmDelete" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Delete
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">
                    <i class="fas fa-times"></i> Cancel
                </button>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast" class="toast"></div>

    <script src="script.js"></script>
</body>
</html>
