const express = require('express')
const dotenv = require('dotenv').config();
const { connectDb } = require('./config/dbConnection');
const app = express();
const errorHandler = require("./MiddleWare/errorHandler")


const port = process.env.PORT || 5000;

app.use(express.json());
app.use("/api/contact", require("./Routes/contactRoutes"));
app.use(errorHandler);

// Connect to database and start server
connectDb().then(() => {
    app.listen(port, () => {
        console.log(`Server is running on port ${port}`);
    });
}).catch((err) => {
    console.error('Failed to connect to database:', err);
    process.exit(1);
});